"use client"

import React, { useState, useR<PERSON>, use<PERSON>ffect, useMemo, useCallback } from "react"
import { ScrollArea } from "@/components/ui/scroll-area"
import { Bot, Download, FileText, Copy, Check, ChevronDown, ChevronUp, Search, Loader2, RefreshCw, Volume2, Play, Pause, ThumbsUp, ThumbsDown, X, ZoomIn, BarChart3, Lightbulb } from "lucide-react"
import ReactMarkdown from "react-markdown"
import remarkGfm from "remark-gfm"
import remarkMath from "@/lib/remarkMath"
import rehypeRaw from "rehype-raw"
import rehypeHighlight from "rehype-highlight"
import { MessageType, AttachmentType } from "./ChatInterface"
import Image from "next/image"
import { cn } from "@/lib/utils"
import { Button } from "@/components/ui/button"
import { Dialog, DialogContent, DialogClose, DialogHeader, DialogTitle, DialogFooter } from "@/components/ui/dialog"
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, TooltipTrigger } from "@/components/ui/tooltip"
import {
  Collapsible,
  CollapsibleContent,
  CollapsibleTrigger,
} from "@/components/ui/collapsible"
import { likeAnswer, dislikeAnswer } from "@/lib/chatService"
import { toast } from "sonner"
import { useWechatWorkAuth } from "@/components/auth/WechatWorkAuth"
import { ttsService } from "@/lib/ttsService"
import { ExternalBrowserLink } from "@/components/wechat/ExternalBrowserLink"
import ErrorBoundary from "@/components/ErrorBoundary" // 导入错误边界组件
import { FeatureCards } from "./FeatureCards"
import { FeedbackGuide } from "./FeedbackGuide"
import { getVersionUpdates } from "@/config/versionUpdates" // 导入版本更新配置
import { matchFormHandler, formatFormData } from "@/config/formHandlers" // 导入表单处理配置

// 导入分拆出的组件
import MermaidChart, { initializeMermaid } from "@/components/chat/MermaidChart"
import CodeBlock from "./CodeBlock"
import ChartBlock from "./ChartBlock"
import EChartsBlock from "./EChartsBlock"
import ChatAvatar from "./ChatAvatar"
import HTMLFormRenderer from "./HTMLFormRenderer"
import DateTimePicker from "./DateTimePicker"
import MathBlock from "./MathBlock"

// 现代loading组件
const ModernLoader = ({ size = 16, className = "" }: { size?: number, className?: string }) => {
  return (
    <div className={cn("relative", className)} style={{ width: size, height: size }}>
      <div
        className="absolute inset-0 rounded-full animate-spin"
        style={{
          background: 'conic-gradient(from 0deg, transparent, #3b82f6, #8b5cf6, transparent)',
          mask: 'radial-gradient(farthest-side, transparent calc(100% - 2px), black calc(100% - 2px))',
          WebkitMask: 'radial-gradient(farthest-side, transparent calc(100% - 2px), black calc(100% - 2px))'
        }}
      />
    </div>
  )
}

// 初始化mermaid配置
initializeMermaid();

// 添加消息内容滚动条的全局样式
const scrollbarStyles = `
  .custom-scrollbar::-webkit-scrollbar {
    width: 5px;
    height: 5px;
  }

  .custom-scrollbar::-webkit-scrollbar-track {
    background: transparent;
  }

  .custom-scrollbar::-webkit-scrollbar-thumb {
    background-color: rgba(155, 155, 155, 0.5);
    border-radius: 20px;
  }

  .custom-scrollbar::-webkit-scrollbar-thumb:hover {
    background-color: rgba(155, 155, 155, 0.7);
  }
`;

// 添加动画延迟类
const animationDelayStyle = `
  .delay-75 {
    animation-delay: 0.75s;
  }

  .delay-150 {
    animation-delay: 1.5s;
  }
`;

// 思考过程可折叠组件（集成到消息内部）
const ThinkingSection = ({ thinking, marker }: { thinking: string, marker?: string }) => {
  const [isOpen, setIsOpen] = useState(true) // 默认展开
  const thinkingContentRef = useRef<HTMLDivElement>(null)

  // 判断是否为 DeepSeek-R1 模型格式
  const isDeepSeekFormat = marker === "<think>"

  // 当思考内容更新时，自动滚动到底部
  useEffect(() => {
    if (isOpen && thinkingContentRef.current) {
      const element = thinkingContentRef.current
      // 检查内容是否超出容器高度
      if (element.scrollHeight > element.clientHeight) {
        element.scrollTop = element.scrollHeight
      }
    }
  }, [thinking, isOpen])

  return (
    <div className="border-b border-blue-200/60 dark:border-blue-800/60 pb-2 mb-2">
      <Collapsible
        open={isOpen}
        onOpenChange={setIsOpen}
        className="w-full rounded-t-md overflow-hidden bg-gradient-to-r from-blue-50/30 to-purple-50/30 dark:from-blue-950/20 dark:to-purple-950/20"
      >
        <CollapsibleTrigger asChild>
          <Button
            variant="ghost"
            size="sm"
            className={cn(
              "h-auto w-full py-1.5 px-3 flex justify-between items-center rounded-md",
              isDeepSeekFormat ? (
                isOpen
                  ? "bg-gradient-to-r from-blue-100/40 to-purple-100/40 hover:from-blue-100/50 hover:to-purple-100/50 dark:from-blue-900/30 dark:to-purple-900/30 dark:hover:from-blue-900/40 dark:hover:to-purple-900/40"
                  : "bg-gradient-to-r from-blue-100/20 to-purple-100/20 hover:from-blue-100/30 hover:to-purple-100/30 dark:from-blue-900/10 dark:to-purple-900/10 dark:hover:from-blue-900/20 dark:hover:to-purple-900/20"
              ) : (
                isOpen
                  ? "bg-gradient-to-r from-blue-50/30 to-purple-50/30 hover:from-blue-50/40 hover:to-purple-50/40 dark:from-blue-950/20 dark:to-purple-950/20 dark:hover:from-blue-950/30 dark:hover:to-purple-950/30"
                  : "hover:bg-gradient-to-r hover:from-blue-50/20 hover:to-purple-50/20 dark:hover:from-blue-950/10 dark:hover:to-purple-950/10"
              )
            )}
          >
            <div className="flex items-center gap-2">
              {isDeepSeekFormat ? (
                <Bot className="h-3.5 w-3.5 text-blue-500" />
              ) : (
                <Search className="h-3.5 w-3.5 text-muted-foreground" />
              )}
              <span className={cn(
                "text-xs font-medium",
                isDeepSeekFormat
                  ? "gradient-text-safe gradient-text-fallback"
                  : (isOpen ? "gradient-text-safe gradient-text-fallback" : "text-muted-foreground")
              )}>
                {isDeepSeekFormat ? "DeepSeek 思考过程" : "思考过程"}
              </span>
            </div>
            <span>
              {isOpen ? <ChevronUp className="h-3.5 w-3.5" /> : <ChevronDown className="h-3.5 w-3.5" />}
            </span>
          </Button>
        </CollapsibleTrigger>
        <CollapsibleContent>
          <div
            ref={thinkingContentRef}
            className={cn(
              "p-3 text-sm text-muted-foreground bg-gradient-to-r from-blue-50/20 to-purple-50/20 dark:from-blue-950/10 dark:to-purple-950/10 border-t border-blue-200/40 dark:border-blue-800/40 max-h-[240px] overflow-y-auto scrollbar-thin", // 从 text-xs 改为 text-sm，添加最大高度限制（约10行）和滚动
              isDeepSeekFormat && "bg-gradient-to-r from-blue-50/40 to-purple-50/40 dark:from-blue-900/20 dark:to-purple-900/20 border-blue-200/60 dark:border-blue-800/60"
            )}
          >
            <ReactMarkdown
              remarkPlugins={[remarkGfm]}
              rehypePlugins={[rehypeRaw]}
              components={{
                p: props => <p className="my-1.5" {...props} />,
                li: props => <li className="my-1" {...props} />,
                ul: props => <ul className="pl-5 list-disc my-2" {...props} />,
                ol: props => <ol className="pl-5 list-decimal my-2" {...props} />
              }}
            >
              {thinking}
            </ReactMarkdown>
          </div>
        </CollapsibleContent>
      </Collapsible>
    </div>
  )
}

// 正在思考组件
const ThinkingIndicator = () => {
  // 使用state存储当前显示的名称
  const [agentName, setAgentName] = useState("MengChat 正在思考...");

  // 监听最新消息内容变化
  useEffect(() => {
    // 创建一个自定义事件监听器，用于接收名称更新
    const handleNameUpdate = (event: CustomEvent) => {
      if (event.detail && event.detail.name) {
        setAgentName(event.detail.name);
      }
    };

    // 添加事件监听器
    window.addEventListener('agent-name-update' as any, handleNameUpdate as EventListener);

    // 清理函数
    return () => {
      window.removeEventListener('agent-name-update' as any, handleNameUpdate as EventListener);
    };
  }, []);

  return (
    <div className="flex items-center justify-between text-sm w-full">
      <span className="px-3 py-1 rounded-full border border-blue-200 dark:border-blue-800 bg-gradient-to-r from-blue-50 to-purple-50 dark:from-blue-950/30 dark:to-purple-950/30 flex-1 text-center">
        <span className="gradient-text-safe gradient-text-fallback font-medium">
          {agentName}
        </span>
      </span>
      <ModernLoader size={16} className="flex-shrink-0 ml-2" />
    </div>
  )
}

// 附件预览组件
const AttachmentPreview = ({ attachment, role }: { attachment: AttachmentType, role?: "user" | "assistant" | "system" }) => {
  // 添加安全检查，确保attachment和其必要属性存在
  if (!attachment || !attachment.type || !attachment.name || !attachment.url) {
    console.warn('AttachmentPreview: attachment or required properties are undefined', attachment);
    return null;
  }
  
  const isImage = attachment.type.startsWith('image/')
  const [isImageDialogOpen, setIsImageDialogOpen] = useState(false)

  // 获取图片URL，优先使用imageId（如果存在且需要解码）
  const getImageUrl = () => {
    if (attachment.imageId) {
      try {
        // 解码imageId获取原始URL
        return atob(attachment.imageId);
      } catch (e) {
        console.error('解码imageId失败:', e);
        // 解码失败则使用原始url
        return attachment.url;
      }
    }
    return attachment.url;
  }

  // 计算实际使用的图片URL
  const imageUrl = isImage ? getImageUrl() : attachment.url;

  return (
    <div className="flex flex-col rounded-md border overflow-hidden bg-muted/30">
      {/* 显示文件信息 */}
      {!isImage && (
        <div className={cn(
          "p-2 flex items-center gap-2 border-b",
          role === "user"
            ? "bg-blue-100/50 dark:bg-blue-900/30" // 用户消息使用浅蓝色背景
            : "bg-gray-50 dark:bg-gray-700"  // 助手消息使用浅灰色背景
        )}>
          <FileText className="h-4 w-4" />
          <span className="text-sm font-medium flex-1 truncate">{attachment.name}</span>
          <span className="text-xs text-muted-foreground">
            {((attachment.size || 0) / 1024).toFixed(1)} KB
          </span>
          {/* 只在助手消息中显示下载按钮 */}
          {role !== "user" && (
            <a
              href={imageUrl}
              download={attachment.name}
              className="text-primary hover:text-primary/80"
            >
              <Download className="h-4 w-4" />
            </a>
          )}
        </div>
      )}
      {isImage && (
        <>
          <div className="p-2 flex justify-center">
            <div
              className="relative overflow-hidden rounded cursor-pointer group"
              onClick={() => setIsImageDialogOpen(true)}
              title="点击查看大图"
            >
              {/* 将宽高缩小为原来的 1/3 */}
              <Image
                src={imageUrl}
                alt={attachment.name}
                width={100}
                height={67}
                className="object-contain max-h-24"
              />
              <div className="absolute inset-0 bg-black/0 group-hover:bg-black/10 flex items-center justify-center opacity-0 group-hover:opacity-100 transition-opacity">
                <ZoomIn className="h-6 w-6 text-white drop-shadow-md" />
              </div>
            </div>
          </div>

          {/* 图片放大对话框 */}
          <Dialog open={isImageDialogOpen} onOpenChange={setIsImageDialogOpen}>
            <DialogContent className="max-w-4xl p-0 overflow-hidden bg-transparent border-0" onClick={(e) => e.stopPropagation()}>
              <DialogHeader className="sr-only">
                <DialogTitle>查看图片</DialogTitle>
              </DialogHeader>
              <div className="relative w-full h-full flex items-center justify-center bg-black/10 backdrop-blur-sm p-2 sm:p-4">
                <Image
                  src={imageUrl}
                  alt={attachment.name}
                  width={1200}
                  height={800}
                  className="object-contain max-h-[80vh] rounded shadow-lg"
                  style={{ maxWidth: '100%' }}
                />
                <DialogClose className="absolute top-2 right-2 bg-black/40 hover:bg-black/60 text-white rounded-full p-1">
                  <X className="h-5 w-5" />
                </DialogClose>
              </div>
            </DialogContent>
          </Dialog>
        </>
      )}
    </div>
  )
}

// 提取消息的思考部分 (如果存在)
const extractThinkingProcess = (content: string): {
  mainContent: string;
  thinkingContent: string | undefined;
  marker: string | undefined
} => {
  // 思考过程常用的标记格式，可以根据实际AI模型输出调整
  const thinkingMarkers = [
    { start: "<思考>", end: "</思考>" },
    { start: "<thinking>", end: "</thinking>" },
    { start: "<Thinking>", end: "</Thinking>" },
    { start: "```thinking", end: "```" },
    { start: "<think>", end: "</think>" }, // DeepSeek-R1 模型格式
  ]

  let resultContent = content;
  let foundThinking: string | undefined = undefined;
  let foundMarker: string | undefined = undefined;

  for (const marker of thinkingMarkers) {
    let currentContent = resultContent;
    let startIndex = currentContent.indexOf(marker.start);

    // 递归处理所有匹配项
    while (startIndex !== -1) {
      const endIndex = currentContent.indexOf(marker.end, startIndex + marker.start.length);

      if (endIndex !== -1) {
        // 提取思考内容 (保存第一个找到的)
        if (!foundThinking) {
          foundThinking = currentContent.substring(
            startIndex + marker.start.length,
            endIndex
          ).trim();
          foundMarker = marker.start;
        }

        // 从内容中移除思考过程部分
        resultContent =
          currentContent.substring(0, startIndex) +
          currentContent.substring(endIndex + marker.end.length);

        // 继续在剩余内容中查找
        currentContent = resultContent;
        startIndex = currentContent.indexOf(marker.start);
      } else {
        // 如果找不到结束标记，直接中断
        break;
      }
    }
  }

  return {
    mainContent: resultContent.trim(),
    thinkingContent: foundThinking,
    marker: foundMarker
  };
}

// 添加消息操作按钮组件
const MessageActions = ({
  content,
  onRegenerate,
  messageId,
  role,
  userQuestion, // 添加用户问题参数
  userAttachments // 添加用户附件参数
}: {
  content: string,
  onRegenerate?: (messageId: string) => void,
  messageId: string,
  role: string,
  userQuestion?: string, // 用户问题
  userAttachments?: AttachmentType[] // 用户附件
}) => {
  const [copied, setCopied] = useState(false)
  const [liked, setLiked] = useState(false)
  const [disliked, setDisliked] = useState(false)
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [speechState, setSpeechState] = useState<'idle' | 'playing' | 'paused' | 'loading'>('idle')
  const [feedbackSubmitted, setFeedbackSubmitted] = useState(false) // 新增：标记是否已提交反馈
  const [hasAudioInstance, setHasAudioInstance] = useState(false) // 新增：标记是否有音频实例
  const { userInfo } = useWechatWorkAuth()
  const isAssistant = role === 'assistant'

  // 从localStorage加载反馈状态
  useEffect(() => {
    if (!messageId || !userInfo?.userId) return;

    try {
      // 使用消息ID和用户ID创建唯一键
      const feedbackKey = `feedback_${userInfo.userId}_${messageId}`;
      const savedFeedback = localStorage.getItem(feedbackKey);

      if (savedFeedback) {
        const { liked: savedLiked, disliked: savedDisliked } = JSON.parse(savedFeedback);
        setLiked(savedLiked || false);
        setDisliked(savedDisliked || false);
        // 如果已经点过赞或踩，标记为已提交反馈
        if (savedLiked || savedDisliked) {
          setFeedbackSubmitted(true);
        }
      }
    } catch (error) {
      console.error('加载反馈状态失败:', error);
    }
  }, [messageId, userInfo?.userId])

  const handleCopy = () => {
    try {
      // 首先尝试使用 navigator.clipboard
      if (navigator.clipboard && navigator.clipboard.writeText) {
        navigator.clipboard.writeText(content)
          .then(() => {
            setCopied(true);
            setTimeout(() => setCopied(false), 2000);
          })
          .catch(fallbackCopy);
      } else {
        fallbackCopy();
      }
    } catch (error) {
      fallbackCopy();
    }
  }

  // 后备复制方法
  const fallbackCopy = () => {
    try {
      const textArea = document.createElement('textarea');
      textArea.value = content;
      textArea.style.position = 'fixed';
      textArea.style.left = '-999999px';
      textArea.style.top = '-999999px';
      document.body.appendChild(textArea);
      textArea.focus();
      textArea.select();

      try {
        document.execCommand('copy');
        setCopied(true);
        setTimeout(() => setCopied(false), 2000);
      } catch (err) {
        console.error('复制失败:', err);
      }

      document.body.removeChild(textArea);
    } catch (err) {
      console.error('复制失败:', err);
    }
  }

  const handleRegenerate = () => {
    if (onRegenerate) {
      onRegenerate(messageId)
    }
  }

  const handleLike = async () => {
    // 如果正在提交或已经提交过反馈，则不允许再次操作
    if (isSubmitting || feedbackSubmitted) return;

    try {
      setIsSubmitting(true);

      // 设置点赞状态为true
      setLiked(true);
      setDisliked(false);

      // 调用API
      if (userInfo?.userId) {
        // 检查是否存在用户问题或附件
        const hasUserQuestion = !!userQuestion && userQuestion.trim() !== '';
        const hasUserAttachments = !!userAttachments && userAttachments.length > 0;

        // 只要存在问题文本或附件，就可以进行反馈
        if (hasUserQuestion || hasUserAttachments) {
          // 准备用于API的用户问题文本
          let userQuestionText = userQuestion || '';

          // 如果有附件，添加附件信息到问题描述中
          if (hasUserAttachments) {
            // 收集所有图片的imageId
            const imageIds = userAttachments
              .filter(attachment => attachment.imageId)
              .map(attachment => attachment.imageId)
              .filter(Boolean);

            // 如果有imageId，使用imageId信息；否则使用数量描述
            if (imageIds.length > 0) {
              const imageIdsStr = imageIds.join(', ');
              userQuestionText = userQuestionText ? `${userQuestionText} [图片ID: ${imageIdsStr}]` : `[图片ID: ${imageIdsStr}]`;
            } else {
              const attachmentsCount = userAttachments.length;
              const attachmentDesc = `[${attachmentsCount}张图片]`;
              userQuestionText = userQuestionText ? `${userQuestionText} ${attachmentDesc}` : attachmentDesc;
            }
          }

          console.log('提交点赞反馈:', userQuestionText);

          const result = await likeAnswer(
            userInfo.userId,
            userQuestionText,
            content
          );

          if (!result) {
            // 如果API调用失败，回滚UI状态
            setLiked(false);
            toast.error("点赞失败，请稍后重试");
            return;
          }

          // API调用成功，保存状态到localStorage
          try {
            const feedbackKey = `feedback_${userInfo.userId}_${messageId}`;
            localStorage.setItem(feedbackKey, JSON.stringify({ liked: true, disliked: false }));
            // 标记已提交反馈，防止重复操作
            setFeedbackSubmitted(true);
          } catch (error) {
            console.error('保存反馈状态失败:', error);
          }
        } else {
          console.warn('无法点赞：缺少用户问题和附件');
          toast.error("无法提交反馈，缺少用户问题");
          setLiked(false);
        }
      }
    } catch (error) {
      console.error("点赞操作失败:", error);
      // 出错时恢复原状态
      setLiked(false);
      toast.error("点赞失败，请稍后重试");
    } finally {
      setIsSubmitting(false);
    }
  }

  const handleDislike = async () => {
    // 如果正在提交或已经提交过反馈，则不允许再次操作
    if (isSubmitting || feedbackSubmitted) return;

    try {
      setIsSubmitting(true);

      // 设置点踩状态为true
      setDisliked(true);
      setLiked(false);

      // 调用API
      if (userInfo?.userId) {
        // 检查是否存在用户问题或附件
        const hasUserQuestion = !!userQuestion && userQuestion.trim() !== '';
        const hasUserAttachments = !!userAttachments && userAttachments.length > 0;

        // 只要存在问题文本或附件，就可以进行反馈
        if (hasUserQuestion || hasUserAttachments) {
          // 准备用于API的用户问题文本
          let userQuestionText = userQuestion || '';

          // 如果有附件，添加附件信息到问题描述中
          if (hasUserAttachments) {
            // 收集所有图片的imageId
            const imageIds = userAttachments
              .filter(attachment => attachment.imageId)
              .map(attachment => attachment.imageId)
              .filter(Boolean);

            // 如果有imageId，使用imageId信息；否则使用数量描述
            if (imageIds.length > 0) {
              const imageIdsStr = imageIds.join(', ');
              userQuestionText = userQuestionText ? `${userQuestionText} [图片ID: ${imageIdsStr}]` : `[图片ID: ${imageIdsStr}]`;
            } else {
              const attachmentsCount = userAttachments.length;
              const attachmentDesc = `[${attachmentsCount}张图片]`;
              userQuestionText = userQuestionText ? `${userQuestionText} ${attachmentDesc}` : attachmentDesc;
            }
          }

          console.log('提交点踩反馈:', userQuestionText);

          const result = await dislikeAnswer(
            userInfo.userId,
            userQuestionText,
            content
          );

          if (!result) {
            // 如果API调用失败，回滚UI状态
            setDisliked(false);
            toast.error("点踩失败，请稍后重试");
            return;
          }

          // API调用成功，保存状态到localStorage
          try {
            const feedbackKey = `feedback_${userInfo.userId}_${messageId}`;
            localStorage.setItem(feedbackKey, JSON.stringify({ liked: false, disliked: true }));
            // 标记已提交反馈，防止重复操作
            setFeedbackSubmitted(true);
          } catch (error) {
            console.error('保存反馈状态失败:', error);
          }
        } else {
          console.warn('无法点踩：缺少用户问题和附件');
          toast.error("无法提交反馈，缺少用户问题");
          setDisliked(false);
        }
      }
    } catch (error) {
      console.error("点踩操作失败:", error);
      // 出错时恢复原状态
      setDisliked(false);
      toast.error("点踩失败，请稍后重试");
    } finally {
      setIsSubmitting(false);
    }
  }

  // TTS相关函数
  const handleTTSPlay = async () => {
    // 检查当前消息是否已有音频实例
    const hasAudioForThisMessage = ttsService.hasAudioForMessage(messageId);
    const currentPlaybackState = ttsService.getPlaybackState();
    const currentPlayingMessageId = ttsService.getCurrentMessageId();
    
    console.log('TTS状态检查:', {
      messageId,
      hasAudioForThisMessage,
      currentPlaybackState,
      currentPlayingMessageId,
      speechState,
      hasAudioInstance
    });

    // 如果有其他消息正在播放，先停止它
    if (currentPlayingMessageId && currentPlayingMessageId !== messageId) {
      ttsService.stopAudio();
    }
    
    // 情况1：当前消息正在播放，则暂停
    if (hasAudioForThisMessage && currentPlaybackState === 'playing') {
      try {
        ttsService.pauseAudio();
        setSpeechState('paused');
      } catch (error) {
        console.error('暂停播放失败:', error);
        setSpeechState('idle');
        setHasAudioInstance(false);
      }
      return;
    }

    // 情况2：当前消息已暂停，则恢复播放
    if (hasAudioForThisMessage && currentPlaybackState === 'paused') {
      try {
        await ttsService.resumeAudio();
        setSpeechState('playing');
        
        // 监听播放结束事件
        const checkPlaybackEnd = () => {
          const state = ttsService.getPlaybackState();
          if (state === 'idle' || ttsService.getCurrentMessageId() !== messageId) {
            setSpeechState('idle');
            setHasAudioInstance(false);
          } else {
            // 继续检查
            setTimeout(checkPlaybackEnd, 1000);
          }
        };
        setTimeout(checkPlaybackEnd, 1000);
        
      } catch (error) {
        console.error('恢复播放失败:', error);
        setSpeechState('idle');
        setHasAudioInstance(false);
        toast.error('恢复播放失败，请重新开始播放');
      }
      return;
    }

    // 情况3：开始新的TTS播放（首次播放或重新播放）
    try {
      setSpeechState('loading');
      
      // 清理内容中的Markdown格式和特殊字符，只保留文本内容
      const cleanContent = content
        .replace(/```[\s\S]*?```/g, '') // 移除代码块
        .replace(/`[^`]*`/g, '') // 移除内联代码
        .replace(/\*\*(.*?)\*\*/g, '$1') // 移除粗体标记
        .replace(/\*(.*?)\*/g, '$1') // 移除斜体标记
        .replace(/#{1,6}\s+/g, '') // 移除标题标记
        .replace(/\[([^\]]*)\]\([^)]*\)/g, '$1') // 移除链接，只保留文本
        .replace(/!\[([^\]]*)\]\([^)]*\)/g, '') // 移除图片
        .replace(/^\s*[-*+]\s+/gm, '') // 移除列表标记
        .replace(/^\s*\d+\.\s+/gm, '') // 移除有序列表标记
        .replace(/\n\s*\n/g, '\n') // 移除多余的空行
        .replace(/\s+/g, ' ') // 将多个空格合并为一个
        .trim();

      if (!cleanContent) {
        toast.error('没有可播放的文本内容');
        setSpeechState('idle');
        setHasAudioInstance(false);
        return;
      }

      // 如果内容太长，进行分段处理
      const maxSegmentLength = 2000; // 每段最大2000个字符
      const segments: string[] = [];
      
      // 如果文本超过最大长度，进行智能分段
      if (cleanContent.length > maxSegmentLength) {
        // 按句子分段
        const sentences = cleanContent.match(/[^。！？.!?]+[。！？.!?]+/g) || [cleanContent];
        let currentSegment = '';
        
        for (const sentence of sentences) {
          if ((currentSegment + sentence).length <= maxSegmentLength) {
            currentSegment += sentence;
          } else {
            if (currentSegment) {
              segments.push(currentSegment.trim());
            }
            currentSegment = sentence;
          }
        }
        
        if (currentSegment) {
          segments.push(currentSegment.trim());
        }
      } else {
        segments.push(cleanContent);
      }

      console.log(`准备播放的文本已分为 ${segments.length} 段`);

      // 批量转换所有段落
      const audioSegments: { blob: Blob; index: number }[] = [];
      
      for (let i = 0; i < segments.length; i++) {
        try {
          console.log(`转换第 ${i + 1}/${segments.length} 段...`);
          const audioBlob = await ttsService.textToSpeech(segments[i]);
          audioSegments.push({ blob: audioBlob, index: i });
        } catch (error) {
          console.error(`第 ${i + 1} 段转换失败:`, error);
          throw error;
        }
      }
      
      // 使用队列播放模式
      if (audioSegments.length > 1) {
        // 多段音频使用队列播放
        setSpeechState('playing');
        setHasAudioInstance(true);
        
        await ttsService.createAndPlayQueue(
          audioSegments, 
          messageId,
          // 播放完成回调
          () => {
            setSpeechState('idle');
            setHasAudioInstance(false);
            console.log('所有音频段播放完成');
          },
          // 播放错误回调
          (error) => {
            console.error('播放失败:', error);
            setSpeechState('idle');
            setHasAudioInstance(false);
            toast.error('播放失败，请重试');
          }
        );
      } else {
        // 单段音频使用原有播放方式
        ttsService.createAudio(audioSegments[0].blob, messageId);
        setHasAudioInstance(true);
        
        // 开始播放音频
        setSpeechState('playing');
        await ttsService.playAudio();
        
        // 监听播放结束事件
        const checkPlaybackEnd = () => {
          const state = ttsService.getPlaybackState();
          if (state === 'idle' || ttsService.getCurrentMessageId() !== messageId) {
            setSpeechState('idle');
            setHasAudioInstance(false);
          } else {
            // 继续检查
            setTimeout(checkPlaybackEnd, 1000);
          }
        };
        setTimeout(checkPlaybackEnd, 1000);
      }
      
    } catch (error) {
      console.error('TTS播放失败:', error);
      setSpeechState('idle');
      setHasAudioInstance(false);
      
      let errorMessage = 'TTS功能暂时不可用';
      if (error instanceof Error) {
        if (error.message.includes('未配置API URL')) {
          errorMessage = '请先配置API地址';
        } else if (error.message.includes('TTS API错误')) {
          errorMessage = 'TTS服务暂时不可用，请稍后重试';
        } else if (error.message.includes('音频播放失败')) {
          errorMessage = '音频播放失败，请检查浏览器音频权限';
        }
      }
      
      toast.error(errorMessage);
    }
  }

  // 组件挂载时同步TTS服务状态
  useEffect(() => {
    // 检查当前消息是否有音频实例
    if (ttsService.hasAudioForMessage(messageId)) {
      const state = ttsService.getPlaybackState();
      setSpeechState(state === 'playing' ? 'playing' : state === 'paused' ? 'paused' : 'idle');
      setHasAudioInstance(state !== 'idle');
    }
  }, [messageId]);

  // 组件卸载时停止音频播放
  useEffect(() => {
    return () => {
      // 只在当前消息的音频正在播放时停止
      if (ttsService.getCurrentMessageId() === messageId) {
        ttsService.stopAudio();
        setHasAudioInstance(false);
      }
    };
  }, [messageId]);

  return (
    <div className="flex items-center gap-0.5">
      {isAssistant && (
        <>
          {onRegenerate && (
            <TooltipProvider>
              <Tooltip>
                <TooltipTrigger asChild>
                  <Button
                    variant="ghost"
                    size="icon"
                    className="h-7 w-7 rounded-md hover:bg-muted/20"
                    onClick={handleRegenerate}
                  >
                    <RefreshCw className="h-4 w-4" />
                  </Button>
                </TooltipTrigger>
                <TooltipContent side="bottom">
                  <p>重新生成回答</p>
                </TooltipContent>
              </Tooltip>
            </TooltipProvider>
          )}

          <TooltipProvider>
            <Tooltip>
              <TooltipTrigger asChild>
                <Button
                  variant="ghost"
                  size="icon"
                  className="h-7 w-7 rounded-md hover:bg-muted/20"
                  onClick={handleCopy}
                >
                  {copied ? <Check className="h-4 w-4 text-green-500" /> : <Copy className="h-4 w-4" />}
                </Button>
              </TooltipTrigger>
              <TooltipContent side="bottom">
                <p>{copied ? "已复制!" : "复制消息"}</p>
              </TooltipContent>
            </Tooltip>
          </TooltipProvider>

          {/* TTS播放按钮 */}
          <TooltipProvider>
            <Tooltip>
              <TooltipTrigger asChild>
                <Button
                  variant="ghost"
                  size="icon"
                  className={`h-7 w-7 rounded-md hover:bg-muted/20 ${speechState === 'playing' ? "text-blue-500" : ""}`}
                  onClick={handleTTSPlay}
                  disabled={speechState === 'loading'}
                >
                  {speechState === 'loading' ? (
                    <Loader2 className="h-4 w-4 animate-spin" />
                  ) : speechState === 'playing' ? (
                    <Pause className="h-4 w-4" />
                  ) : speechState === 'paused' ? (
                    <Play className="h-4 w-4 text-blue-500" />
                  ) : (
                    <Volume2 className="h-4 w-4" />
                  )}
                </Button>
              </TooltipTrigger>
              <TooltipContent side="bottom">
                <p>
                  {speechState === 'loading' ? "正在加载..." :
                   speechState === 'playing' ? "暂停播放" :
                   speechState === 'paused' ? "继续播放" :
                   "朗读消息"}
                </p>
              </TooltipContent>
            </Tooltip>
          </TooltipProvider>

          <TooltipProvider>
            <Tooltip>
              <TooltipTrigger asChild>
                <Button
                  variant="ghost"
                  size="icon"
                  className={`h-7 w-7 rounded-md hover:bg-muted/20 ${liked ? "text-blue-500" : ""}`}
                  onClick={handleLike}
                  disabled={isSubmitting || feedbackSubmitted}
                >
                  <ThumbsUp className={`h-4 w-4 ${liked ? "fill-current" : ""}`} />
                </Button>
              </TooltipTrigger>
              <TooltipContent side="bottom">
                <p>{feedbackSubmitted && liked ? "已点赞" : "点赞回答"}</p>
              </TooltipContent>
            </Tooltip>
          </TooltipProvider>

          <TooltipProvider>
            <Tooltip>
              <TooltipTrigger asChild>
                <Button
                  variant="ghost"
                  size="icon"
                  className={`h-7 w-7 rounded-md hover:bg-muted/20 ${disliked ? "text-red-500" : ""}`}
                  onClick={handleDislike}
                  disabled={isSubmitting || feedbackSubmitted}
                >
                  <ThumbsDown className={`h-4 w-4 ${disliked ? "fill-current" : ""}`} />
                </Button>
              </TooltipTrigger>
              <TooltipContent side="bottom">
                <p>{feedbackSubmitted && disliked ? "已点踩" : "点踩回答"}</p>
              </TooltipContent>
            </Tooltip>
          </TooltipProvider>

          {/* 添加反馈/建议按钮 */}
          <TooltipProvider>
            <Tooltip>
              <TooltipTrigger asChild>
                <Button
                  variant="ghost"
                  size="icon"
                  className="h-7 w-7 rounded-md hover:bg-muted/20"
                  onClick={() => window.open("https://www.groupama-sdig.com/cnt/sJhY9D", "_blank")}
                >
                  <Lightbulb className="h-4 w-4 text-amber-500" />
                </Button>
              </TooltipTrigger>
              <TooltipContent side="bottom">
                <p>反馈/建议</p>
              </TooltipContent>
            </Tooltip>
          </TooltipProvider>
        </>
      )}
    </div>
  )
}

interface ChatMessagesProps {
  messages: MessageType[]
  messagesEndRef: React.RefObject<HTMLDivElement>
  onRegenerateMessage?: (messageId: string) => void
  scrollToBottomRef?: React.MutableRefObject<(() => void) | null>
  onSendMessage?: (content: string) => void // Add new prop for sending messages
  isInCenteredLayout?: boolean // 是否在居中布局中（80%宽度）
}

// 清理代码，使用MermaidChart内部的函数名保持一致
function cleanMermaidCode(code: string): string {
  return code
    .replace(/^```mermaid\n?/i, '')
    .replace(/```$/g, '')
    .trim();
}

// 生成图表内容的哈希值为缓存key
function generateChartCacheKey(content: string): string {
  // 使用简单的字符串哈希算法，与MermaidChart组件保持一致
  let hash = 0;
  for (let i = 0; i < content.length; i++) {
    const char = content.charCodeAt(i);
    hash = ((hash << 5) - hash) + char;
    hash = hash & hash; // 转换为32位整数
  }
  return `mermaid-${Math.abs(hash)}`;
}

// 添加版本更新通知组件
const VersionUpdateNotice = () => {
  const [isDialogOpen, setIsDialogOpen] = useState(false);
  const [isVisible, setIsVisible] = useState(true);

  // 从配置中获取版本更新内容
  const updateContents = useMemo(() => {
    const contents = getVersionUpdates();
    // 确保至少有一个版本更新内容
    return contents.length > 0 ? contents : [
      {
        version: "v1.0.0",
        content: "欢迎使用MengChat"
      }
    ];
  }, []);

  // 获取最新版本的信息用于显示
  const latestVersion = useMemo(() => {
    if (updateContents.length === 0) return { version: "v1.0.0", content: "欢迎使用MengChat" };
    return updateContents[0];
  }, [updateContents]);

  // 将版本内容按行分割用于显示
  const contentLines = useMemo(() => {
    const lines = latestVersion.content.split('\n').filter(line => line.trim() !== '');
    return lines.length > 0 ? lines : ["欢迎使用MengChat"];
  }, [latestVersion]);

  // 检查本地存储中的已读版本
  useEffect(() => {
    try {
      // 移除根据已读版本隐藏通知的逻辑，始终显示通知
      setIsVisible(true);
    } catch (error) {
      console.error('读取本地存储失败:', error);
    }
  }, [updateContents]);

  // 点击时打开弹窗
  const handleClick = () => {
    setIsDialogOpen(true);
  };

  // 关闭弹窗
  const handleDialogClose = () => {
    setIsDialogOpen(false);
  };

  // 如果不可见，不渲染组件
  if (!isVisible) {
    return null;
  }

  return (
    <>
      <div
        className="mt-4 border rounded-md overflow-hidden bg-gradient-to-r from-blue-50 to-indigo-50 dark:from-blue-950/30 dark:to-indigo-950/30 cursor-pointer hover:shadow-md transition-shadow"
        onClick={handleClick}
      >
        <div className="px-4 py-2 bg-blue-100/50 dark:bg-blue-900/20 border-b flex items-center justify-between">
          <span className="text-sm font-medium text-blue-700 dark:text-blue-300 flex items-center gap-1">
            <BarChart3 className="h-3.5 w-3.5" /> MengChat助手更新通知
            {updateContents.length > 0 && (
              <span className="ml-2 text-xs bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200 px-1.5 py-0.5 rounded-full">
                {latestVersion.version}
              </span>
            )}
          </span>
          <span className="text-xs text-blue-600/70 dark:text-blue-400/70">点击查看详情</span>
        </div>
        <div className="p-3">
          <div
            className="font-medium text-gray-800 dark:text-gray-200 space-y-1 max-h-[4.5rem] overflow-y-auto custom-scrollbar text-left"
            style={{ lineHeight: '1.5rem' }} // 每行1.5rem，3行就是4.5rem
          >
            {contentLines.map((line: string, index: number) => (
              <div key={index} className="leading-relaxed text-left">
                {line}
              </div>
            ))}
          </div>
        </div>
      </div>

      {/* 版本更新详情弹窗 */}
      <Dialog open={isDialogOpen} onOpenChange={handleDialogClose}>
        <DialogContent className="max-w-2xl">
          <DialogHeader>
            <DialogTitle>版本更新详情</DialogTitle>
          </DialogHeader>
          <div className="py-4">
            {updateContents.length > 0 ? (
              <div>
                <div className="flex items-center gap-2 mb-4">
                  <div className="font-semibold text-blue-600 dark:text-blue-400">{latestVersion.version}</div>
                  <span className="px-1.5 py-0.5 text-xs bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200 rounded-full">最新版本</span>
                </div>
                <div className="text-gray-700 dark:text-gray-300 space-y-3">
                  {contentLines.map((line: string, index: number) => (
                    <p key={index} className="leading-relaxed">{line}</p>
                  ))}
                </div>
              </div>
            ) : (
              <div className="text-center py-8 text-gray-500">
                暂无版本更新内容
              </div>
            )}
          </div>
          <DialogFooter>
            <Button variant="outline" onClick={handleDialogClose}>关闭</Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </>
  );
};

export default function ChatMessages({ messages, messagesEndRef, onRegenerateMessage, scrollToBottomRef, onSendMessage, isInCenteredLayout = true }: ChatMessagesProps) {
  const scrollAreaRef = useRef<HTMLDivElement>(null)
  const [isScrolledToBottom, setIsScrolledToBottom] = useState(true)
  const [isAutoScrollEnabled, setIsAutoScrollEnabled] = useState(true)
  const [isScrolling, setIsScrolling] = useState(false)
  const scrollTimer = useRef<NodeJS.Timeout | null>(null)


  const userScrolledUp = useRef(false)
  const lastScrollPosition = useRef(0)
  const scrollLock = useRef(false)
  // 添加图表渲染状态追踪
  const [chartsRendering, setChartsRendering] = useState(0)
  const pendingChartsRef = useRef(0)
  const chartCacheRef = useRef<Map<string, boolean>>(new Map())
  const hasCountedCharts = useRef(new Set<string>())
  // 添加最后一次消息更新的时间戳，用于检测新消息
  const lastMessageUpdateRef = useRef<number>(0)
  // 添加延迟滚动定时器引用数组，方便清除所有定时器
  const delayScrollTimers = useRef<NodeJS.Timeout[]>([])
  // 添加一个属性跟踪最后一条消息ID，用于检测新消息
  const lastMessageIdRef = useRef<string>("")
  // 添加反馈引导相关状态
  const [showFeedbackGuide, setShowFeedbackGuide] = useState(false)
  const { userInfo } = useWechatWorkAuth()

  // 默认表单处理函数（作为后备方案）

  const handleDefaultForm = useCallback((formData: FormData, formElement: HTMLFormElement) => {
    console.log('处理默认表单:', formData)

    const dataEntries = Array.from(formData.entries())
    const formDataText = dataEntries
      .map(([key, value]) => `${key}: ${value}`)
      .join('\n')

    if (onSendMessage) {
      onSendMessage(`表单数据:\n${formDataText}`)
    }

    toast.success('表单已提交！')
  }, [onSendMessage])

  // 根据屏幕尺寸和布局类型计算消息框固定宽度 - 使用useMemo避免重复计算
  const messageWidth = useMemo(() => {
    // 在小屏幕上使用更高的百分比
    if (typeof window !== 'undefined' && window.innerWidth < 768) {
      return 'calc(100% - 2.5rem)'; // 小屏幕宽度，减少左右边距使消息框更宽
    }

    // 根据是否在居中布局中调整宽度
    if (isInCenteredLayout) {
      // 在80%宽度的容器内，用户消息使用适中的宽度
      return 'min(85%, 48rem)'; // 增加到85%，最大宽度48rem，充分利用80%容器的空间
    } else {
      // 在100%宽度的容器内，使用100%宽度
      return '100%';
    }
  }, [isInCenteredLayout]);

  // 计算包含Mermaid图表的消息宽度 - AI消息统一使用100%宽度
  const mermaidMessageWidth = useMemo(() => {
    // 在小屏幕上使用更高的百分比
    if (typeof window !== 'undefined' && window.innerWidth < 768) {
      return 'calc(100% - 1rem)'; // 小屏幕上几乎全宽
    }

    // AI消息始终使用100%宽度，无论在哪种布局中
    return '100%';
  }, []);

  // 使用useEffect将自定义滚动条样式添加到页面
  useEffect(() => {
    // 创建样式元素
    const styleElement = document.createElement('style');
    styleElement.innerHTML = scrollbarStyles + animationDelayStyle;
    document.head.appendChild(styleElement);

    // 清理函数
    return () => {
      document.head.removeChild(styleElement);
    };
  }, []);

  // 清除所有滚动定时器的辅助函数
  const clearAllScrollTimers = useCallback(() => {
    // 清除主滚动定时器
    if (scrollTimer.current) {
      clearTimeout(scrollTimer.current);
      scrollTimer.current = null;
    }

    // 清除所有延迟滚动定时器
    if (delayScrollTimers.current.length > 0) {
      delayScrollTimers.current.forEach(timer => clearTimeout(timer));
      delayScrollTimers.current = [];
    }
  }, []);

  // 滚动到底部的方法
  const scrollToBottom = useCallback(() => {
    // 如果用户已经向上滚动，不执行自动滚动
    if (userScrolledUp.current) {
      console.log('用户已向上滚动，跳过自动滚动');
      return;
    }

    // 先重置滚动状态变量
    setIsAutoScrollEnabled(true);

    // 清除所有滚动定时器
    clearAllScrollTimers();

    // 如果滚动区域不存在，直接返回
    if (!scrollAreaRef.current) return;

    // 如果滚动锁定中，先解除锁定再滚动
    if (scrollLock.current) {
      scrollLock.current = false;
    }

    try {
      setIsScrolling(true);

      if (messagesEndRef.current) {
        // 检查是否有流式消息正在输出
        const lastMessage = messages.length > 0 ? messages[messages.length - 1] : null;
        const isStreamingMessage = lastMessage && lastMessage.isStreaming;

        // 使用auto滚动行为以提高可靠性
        const scrollBehavior = "auto";

        // 使用多种方式滚动，增加可靠性
        // 1. scrollIntoView
        messagesEndRef.current.scrollIntoView({ behavior: scrollBehavior });

        // 2. 直接设置 scrollTop
        const scrollArea = scrollAreaRef.current?.querySelector('[data-radix-scroll-area-viewport]') as HTMLElement;
        if (scrollArea) {
          const scrollHeight = scrollArea.scrollHeight;
          const clientHeight = scrollArea.clientHeight;
          scrollArea.scrollTop = scrollHeight - clientHeight;
        }

        // 3. 使用原生滚动
        if (scrollAreaRef.current) {
          const scrollHeight = scrollAreaRef.current.scrollHeight;
          const clientHeight = scrollAreaRef.current.clientHeight;
          scrollAreaRef.current.scrollTop = scrollHeight - clientHeight;
        }

        // 4. 使用window滚动
        window.scrollTo(0, document.body.scrollHeight);

        // 只对非流式消息使用延迟滚动，且只有在用户没有向上滚动时
        if (!isStreamingMessage && !userScrolledUp.current) {
          // 设置一系列延迟滚动，确保在DOM完全渲染后滚动到底部
          // 将定时器引用保存到数组中，以便于后续清除
          const timer1 = setTimeout(() => {
            if (!userScrolledUp.current && messagesEndRef.current) {
              messagesEndRef.current.scrollIntoView({ behavior: "auto" });

              // 再次尝试所有滚动方法
              if (scrollArea) {
                const scrollHeight = scrollArea.scrollHeight;
                const clientHeight = scrollArea.clientHeight;
                scrollArea.scrollTop = scrollHeight - clientHeight;
              }

              if (scrollAreaRef.current) {
                const scrollHeight = scrollAreaRef.current.scrollHeight;
                const clientHeight = scrollAreaRef.current.clientHeight;
                scrollAreaRef.current.scrollTop = scrollHeight - clientHeight;
              }

              window.scrollTo(0, document.body.scrollHeight);
            }
          }, 50);

          const timer2 = setTimeout(() => {
            if (!userScrolledUp.current && messagesEndRef.current) {
              messagesEndRef.current.scrollIntoView({ behavior: "auto" });
            }
          }, 150);

          const timer3 = setTimeout(() => {
            if (!userScrolledUp.current && messagesEndRef.current) {
              messagesEndRef.current.scrollIntoView({ behavior: "auto" });
            }
          }, 300);

          // 保存定时器引用
          delayScrollTimers.current.push(timer1, timer2, timer3);
        }

        // 设置短暂的滚动锁，防止后续滚动事件干扰
        scrollLock.current = true;

        // 对于流式消息使用更短的锁定时间
        const lockTime = isStreamingMessage ? 100 : 400;

        scrollTimer.current = setTimeout(() => {
          setIsScrolledToBottom(true);
          setIsScrolling(false);
          scrollLock.current = false;

          // 只有在用户没有向上滚动的情况下，再次尝试滚动
          if (!userScrolledUp.current && messagesEndRef.current) {
            messagesEndRef.current.scrollIntoView({ behavior: "auto" });
          }
        }, lockTime);
      }
    } catch (e) {
      console.error("Scroll error:", e);
      setIsScrolling(false);
      scrollLock.current = false;
    }
  }, [messagesEndRef, messages, clearAllScrollTimers]);

  // 将滚动函数暴露给父组件
  useEffect(() => {
    if (scrollToBottomRef) {
      scrollToBottomRef.current = scrollToBottom;
    }
  }, [scrollToBottom, scrollToBottomRef]);

  // 检测消息中是否包含图表，并更新渲染计数
  useEffect(() => {
    const lastMessage = messages.length > 0 ? messages[messages.length - 1] : null;
    // 如果没有新消息，不处理
    if (!lastMessage || !lastMessage.content) return;

    // 如果是流式输出或者isStreaming标记为true，暂不处理图表计数
    if (lastMessage.isStreaming) {
      return;
    }

    // 只有新消息才检查图表
    let newCharts = 0;

    // 检查mermaid图表
    const mermaidMatches = lastMessage.content.match(/```mermaid\n([\s\S]*?)```/g);
    if (mermaidMatches) {
      mermaidMatches.forEach(chartCode => {
        // 使用稳定的缓存key
        const cleanedCode = cleanMermaidCode(chartCode);
        const cacheKey = generateChartCacheKey(cleanedCode);

        // 如果这个图表已经被计数过，跳过
        if (hasCountedCharts.current.has(cacheKey)) {
          return;
        }

        // 标记图表已计数，避免重复计算
        hasCountedCharts.current.add(cacheKey);
        newCharts++;
      });
    }

    // 检查其他图表 (chart和echarts类型)
    const otherChartMatches = lastMessage.content.match(/```(chart|echarts)\n([\s\S]*?)```/g);
    if (otherChartMatches) {
      otherChartMatches.forEach(chartCode => {
        const cacheKey = chartCode.trim();

        // 如果这个图表已经被计数过，跳过
        if (hasCountedCharts.current.has(cacheKey)) {
          return;
        }

        // 标记图表已计数，避免重复计算
        hasCountedCharts.current.add(cacheKey);
        newCharts++;
      });
    }

    // 只有有新图表时才更新状态
    if (newCharts > 0) {
      console.log(`发现${newCharts}个新图表`);
      pendingChartsRef.current += newCharts;
      setChartsRendering(prev => prev + newCharts);
    }
  }, [messages]);

  // 检测是否应该显示反馈引导
  useEffect(() => {
    // 检查是否为新会话的第一次AI回复完成
    if (messages.length !== 2) return;

    const [userMsg, aiMsg] = messages;
    if (userMsg.role !== 'user' || aiMsg.role !== 'assistant') return;
    if (aiMsg.isStreaming || aiMsg.isThinking) return;

    // 检查用户是否已看过引导
    try {
      const guideKey = `feedback_guide_shown_${userInfo?.userId || 'anonymous'}`;
      const hasShown = localStorage.getItem(guideKey);

      if (!hasShown) {
        // 延迟显示引导，让用户先看到AI回复
        const timer = setTimeout(() => {
          setShowFeedbackGuide(true);
        }, 1000);

        return () => clearTimeout(timer);
      }
    } catch (error) {
      console.error('检查反馈引导状态失败:', error);
    }
  }, [messages, userInfo]);

  // 关闭反馈引导
  const handleCloseFeedbackGuide = useCallback(() => {
    setShowFeedbackGuide(false);
  }, []);

  // 图表渲染完成后的处理
  useEffect(() => {
    if (chartsRendering > 0) {
      // 设置一个安全的超时时间，防止图表渲染卡住
      const timer = setTimeout(() => {
        console.log('图表渲染超时，强制重置');
        setChartsRendering(0);
        pendingChartsRef.current = 0;

        // 图表渲染完成或超时后，如果在底部，滚动到底部
        if (isScrolledToBottom && !userScrolledUp.current) {
          scrollToBottom();
        }
      }, 5000); // 增加超时时间到5秒

      return () => clearTimeout(timer);
    } else if (pendingChartsRef.current === 0 && isScrolledToBottom) {
      // 所有图表渲染完成，执行一次滚动
      const scrollTimer = setTimeout(() => {
        if (isScrolledToBottom && !userScrolledUp.current) {
          scrollToBottom();
        }
      }, 200);

      return () => clearTimeout(scrollTimer);
    }
  }, [chartsRendering, isScrolledToBottom, scrollToBottom]);

  // 监听滚动事件，检测用户是否向上滚动
  useEffect(() => {
    const scrollArea = scrollAreaRef.current?.querySelector('[data-radix-scroll-area-viewport]') as HTMLElement
    if (!scrollArea) return;

    let scrollTimeout: NodeJS.Timeout | null = null;

    const handleScroll = () => {
      // 如果正在系统触发的滚动中，忽略此事件
      if (scrollLock.current) return;

      // 清除之前的防抖定时器
      if (scrollTimeout) {
        clearTimeout(scrollTimeout);
      }

      const { scrollTop, scrollHeight, clientHeight } = scrollArea;

      // 检测用户是否向上滚动
      if (scrollTop < lastScrollPosition.current) {
        // 如果用户向上滚动，立即标记并停止所有自动滚动
        if (!userScrolledUp.current) {
          console.log('检测到用户向上滚动，停止所有自动滚动');
          userScrolledUp.current = true;
          setIsAutoScrollEnabled(false);
          // 立即清除所有可能导致滚动的定时器
          clearAllScrollTimers();
        }
      }

      // 更新上次滚动位置
      lastScrollPosition.current = scrollTop;

      // 检测是否在底部
      const isBottom = Math.abs(scrollHeight - scrollTop - clientHeight) < 20;

      scrollTimeout = setTimeout(() => {
        setIsScrolledToBottom(isBottom);

        // 只有当用户显式滚动到底部时，才重新启用自动滚动
        if (isBottom && !scrollLock.current) {
          setIsAutoScrollEnabled(true);
          userScrolledUp.current = false;
        }
      }, 150);
    };

    scrollArea.addEventListener('scroll', handleScroll);

    return () => {
      if (scrollArea) {
        scrollArea.removeEventListener('scroll', handleScroll);
      }
      if (scrollTimeout) {
        clearTimeout(scrollTimeout);
      }
    };
  }, [clearAllScrollTimers]);

  // 修改自动滚动到底部当消息更新时的处理逻辑
  useEffect(() => {
    // 获取最后一条消息
    const lastMessage = messages.length > 0 ? messages[messages.length - 1] : null;

    // 如果没有消息，不处理
    if (!lastMessage) return;

    // 使用消息ID检测是否有新消息（比时间戳更可靠）
    const isNewMessage = lastMessage.id !== lastMessageIdRef.current;

    if (isNewMessage) {
      // 更新最后消息ID和时间戳
      lastMessageIdRef.current = lastMessage.id;
      lastMessageUpdateRef.current = Date.now();

      // 在新消息到达时，重置用户滚动状态和启用自动滚动
      // 但是仅在以下情况下这样做：
      // 1. 用户发送的消息
      // 2. 助手开始回复的消息
      // 其他情况下，保持用户的滚动位置
      const isUserMessage = lastMessage.role === 'user';
      const isNewAssistantMessage = lastMessage.role === 'assistant' &&
                                    (lastMessage.isStreaming || lastMessage.isThinking);

      // 只有在新用户消息或助手开始回复时重置滚动
      if (isUserMessage || isNewAssistantMessage) {
        console.log('新消息到达：', isUserMessage ? '用户消息' : '助手回复开始');
        userScrolledUp.current = false;
        setIsAutoScrollEnabled(true);
        scrollToBottom();
      }
      // 对于非流式的助手消息（一次性返回的大量内容），需要特殊处理
      else if (lastMessage.role === 'assistant' && !lastMessage.isStreaming && !lastMessage.isThinking) {
        console.log('检测到新的非流式助手消息');

        // 如果用户没有主动向上滚动，则自动滚动到底部
        if (!userScrolledUp.current) {
          console.log('用户未主动向上滚动，执行自动滚动');
          scrollToBottom();

          // 添加延迟滚动以确保完全渲染后的内容可见
          // 将定时器引用保存到数组中
          const timer1 = setTimeout(scrollToBottom, 300);
          const timer2 = setTimeout(scrollToBottom, 800);
          delayScrollTimers.current.push(timer1, timer2);
        } else {
          console.log('用户已向上滚动，保持当前滚动位置');
        }
      }
    }

    // 处理流式消息的滚动
    const isStreamingMessage = lastMessage && lastMessage.isStreaming;

    if (isStreamingMessage && !userScrolledUp.current) {
      // 流式消息更新且用户未向上滚动时，使用节流的方式滚动
      if (!scrollLock.current) {
        scrollToBottom();
      }
    }
  }, [messages, scrollToBottom, clearAllScrollTimers]);

  // 替换之前的MutationObserver实现，使其尊重用户滚动行为
  useEffect(() => {
    // 监听整个消息区域的变化
    const messagesContainer = scrollAreaRef.current;
    if (!messagesContainer) return;

    // 获取最后一条消息
    const lastMessage = messages.length > 0 ? messages[messages.length - 1] : null;

    // 创建一个新的MutationObserver来监视DOM变化
    const observer = new MutationObserver((mutations) => {
      // 只检查子节点添加/删除的变化，不关注其他DOM变化
      const hasNewNodes = mutations.some(mutation =>
        mutation.type === 'childList' && mutation.addedNodes.length > 0
      );

      // 只有在用户没有向上滚动的情况下，才考虑自动滚动
      if (!userScrolledUp.current && isAutoScrollEnabled) {
        const isLastMessageStreaming = lastMessage && lastMessage.isStreaming;

        // 对于流式消息或检测到DOM有新节点时，滚动到底部
        if (hasNewNodes && isLastMessageStreaming) {
          // 使用requestAnimationFrame确保在下一帧渲染前执行
          requestAnimationFrame(() => {
            if (!userScrolledUp.current) { // 再次检查，确保滚动时用户没有向上滚动
              scrollToBottom();
            }
          });
        }
      }
    });

    // 减少观察的范围和类型，仅关注与滚动相关的必要变化
    observer.observe(messagesContainer, {
      childList: true,     // 观察子节点添加或删除
      subtree: true,       // 观察后代节点
      characterData: false, // 不观察文本变化
      attributes: false     // 不观察属性变化
    });

    // 清理函数
    return () => {
      observer.disconnect();
    };
  }, [scrollToBottom, isAutoScrollEnabled, messages]);

  // 清理所有定时器
  useEffect(() => {
    return () => {
      clearAllScrollTimers();
    };
  }, [clearAllScrollTimers]);

  if (messages.length === 0) {
    return (
      <div className="relative flex-1 flex flex-col items-center justify-center p-4 text-center" style={{ marginTop: '0%' }}>


        <h3 className="text-xl font-semibold mb-4">我能帮你做什么？</h3>
        <div data-tour="feature-cards">
          <FeatureCards onSendMessage={onSendMessage || (() => {})} />
        </div>
        <div className="mt-6 text-sm text-muted-foreground">
          <p>点击上方卡片或在下方输入框中发送消息开始聊天</p>
        </div>

        {/* 添加版本更新通知组件 */}
        {/* <div className="w-full max-w-4xl px-4">
          <VersionUpdateNotice />
        </div> */}
      </div>
    )
  }

  // 打字机效果组件
  const TypewriterText = ({
    content,
    isStreaming,
    showThinking = true,
    role = "assistant",
    onSendMessage
  }: {
    content: string,
    isStreaming?: boolean,
    showThinking?: boolean,
    role?: "user" | "assistant" | "system",
    onSendMessage?: (content: string) => void
  }) => {
    const [displayedContent, setDisplayedContent] = useState(content);
    const [isThinking, setIsThinking] = useState(false);
    const [thinkingContent, setThinkingContent] = useState("");
    const prevContentRef = useRef(content);
    const streamingThinkingRef = useRef<HTMLDivElement>(null);

    // 当流式思考内容更新时，自动滚动到底部
    useEffect(() => {
      if (isStreaming && streamingThinkingRef.current && (isThinking || thinkingContent)) {
        const element = streamingThinkingRef.current
        // 检查内容是否超出容器高度
        if (element.scrollHeight > element.clientHeight) {
          element.scrollTop = element.scrollHeight
        }
      }
    }, [displayedContent, isThinking, thinkingContent, isStreaming])

    useEffect(() => {
      // 如果内容没有变化，不做任何处理
      if (content === prevContentRef.current) {
        return;
      }

      // 如果新内容比之前的短，直接更新（可能是重置）
      if (content.length < prevContentRef.current.length) {
        setDisplayedContent(content);
        prevContentRef.current = content;
        return;
      }

      // 获取新增的内容部分
      const newChars = content.slice(prevContentRef.current.length);

      // 更新引用值，避免在动画过程中被覆盖
      prevContentRef.current = content;

      // 如果不是流式输出或者内容为空，直接显示全部内容
      if (!isStreaming || !displayedContent) {
        setDisplayedContent(content);

        // 只有助手消息才检查思考过程标记
        if (role === "assistant") {
          // 检查是否包含完整的思考过程标记
          const thinkStart = content.indexOf("<think>");
          const thinkEnd = content.indexOf("</think>");

          if (thinkStart !== -1 && thinkEnd !== -1 && thinkEnd > thinkStart) {
            setIsThinking(false);
            const extractedThinking = content.substring(thinkStart + 7, thinkEnd).trim();
            setThinkingContent(extractedThinking);
          }
        }
        return;
      }

      // 检查新增内容中是否包含思考标记
      if (role === "assistant") {
        // 只在助手消息中处理思考标记
        if (newChars.includes("<think>")) {
          // 设置正在思考状态
          setIsThinking(true);

          // 如果在同一个增量中就包含了完整的思考过程，提取出来
          const fullContent = displayedContent + newChars;
          const thinkStart = fullContent.indexOf("<think>");
          const thinkEnd = fullContent.indexOf("</think>");

          if (thinkStart !== -1 && thinkEnd !== -1 && thinkEnd > thinkStart) {
            // 完整的思考过程
            setIsThinking(false);
            const extractedThinking = fullContent.substring(thinkStart + 7, thinkEnd).trim();
            setThinkingContent(extractedThinking);
          }
        } else if (newChars.includes("</think>")) {
          // 思考过程结束
          setIsThinking(false);

          // 提取完整的思考内容
          const fullContent = displayedContent + newChars;
          const thinkStart = fullContent.indexOf("<think>");
          const thinkEnd = fullContent.indexOf("</think>");

          if (thinkStart !== -1 && thinkEnd !== -1 && thinkEnd > thinkStart) {
            const extractedThinking = fullContent.substring(thinkStart + 7, thinkEnd).trim();
            setThinkingContent(extractedThinking);
          }
        }
      }

      // 逐字添加新字符，使用更大的批量和更长的间隔来显著减少状态更新次数
      let index = 0;
      const batchSize = 20; // 增加批量大小到 20 个字符
      const updateInterval = 100; // 更新间隔增加到 100ms

      // 如果内容很短，直接显示而不使用动画
      if (newChars.length < 50) {
        setDisplayedContent(prevContentRef.current + newChars);
        return;
      }

      const timer = setInterval(() => {
        if (index < newChars.length) {
          // 计算这一批要添加的字符
          const endIndex = Math.min(index + batchSize, newChars.length);
          const batch = newChars.substring(index, endIndex);

          setDisplayedContent(prev => prev + batch);
          index = endIndex;
        } else {
          clearInterval(timer);
        }
      }, updateInterval); // 显著增加间隔时间，减少更新频率

      return () => clearInterval(timer);
    }, [content, isStreaming, role]);

    // 在渲染时清理显示的内容，移除思考过程标记
    const cleanDisplayContent = (text: string): string => {
      let cleaned = text;

      // 只在助手消息中移除思考过程标记及内容
      if (role === "assistant") {
        // 移除思考过程标记及内容
        const thinkStart = cleaned.indexOf("<think>");
        if (thinkStart !== -1) {
          const thinkEnd = cleaned.indexOf("</think>");

          if (thinkEnd !== -1 && thinkEnd > thinkStart) {
            // 完整的思考过程，移除标签和内容
            cleaned = cleaned.substring(0, thinkStart) + cleaned.substring(thinkEnd + 8);
          } else {
            // 只有开始标签，保留开始标签前的内容
            cleaned = cleaned.substring(0, thinkStart);
          }

          // 递归处理可能存在的多个思考标记
          return cleanDisplayContent(cleaned);
        }
      }

      // 确保即使清理后为空也至少返回一个空格，避免完全空白
      return cleaned.trim() || " ";
    };

    const getThinkingContent = () => {
      // 只在助手消息中提取思考内容
      if (role !== "assistant") return "";

      if (!isThinking && thinkingContent) {
        return thinkingContent;
      }

      // 在思考过程中，实时提取正在输出的思考内容
      const thinkStart = displayedContent.indexOf("<think>");
      if (thinkStart !== -1) {
        const thinkEnd = displayedContent.indexOf("</think>");
        if (thinkEnd !== -1 && thinkEnd > thinkStart) {
          // 完整的思考过程
          return displayedContent.substring(thinkStart + 7, thinkEnd);
        } else {
          // 正在输出的思考过程
          return displayedContent.substring(thinkStart + 7);
        }
      }

      return "";
    };

    const renderThinkingSection = () => {
      // 如果禁用显示思考过程，直接返回null
      if (!showThinking) return null;

      // 只有助手消息才显示思考过程
      if (role !== "assistant") return null;

      // 只有在流式输出且有思考标签时才显示思考过程提示框
      if (!isStreaming) return null;

      // 检查是否有思考标签
      const hasThinkTag = isThinking || thinkingContent || displayedContent.includes("<think>");
      if (!hasThinkTag) return null;

      // 获取当前的思考内容
      const currentThinkingContent = getThinkingContent();

      return (
        <div className="border-b border-blue-200/60 dark:border-blue-800/60 pb-2 mb-2">
          <Collapsible
            open={true}
            className="w-full rounded-t-md overflow-hidden bg-gradient-to-r from-blue-50/30 to-purple-50/30 dark:from-blue-950/20 dark:to-purple-950/20"
          >
            <CollapsibleTrigger asChild>
              <Button
                variant="ghost"
                size="sm"
                className={cn(
                  "h-auto w-full py-1.5 px-3 flex justify-between items-center rounded-md",
                  isThinking
                    ? "bg-gradient-to-r from-blue-100/50 to-purple-100/50 dark:from-blue-900/40 dark:to-purple-900/40"
                    : "bg-gradient-to-r from-blue-100/30 to-purple-100/30 dark:from-blue-900/20 dark:to-purple-900/20"
                )}
                onClick={(e) => e.preventDefault()}
              >
                <div className="flex items-center justify-between w-full">
                  <div className="flex items-center gap-2">
                    <Bot className="h-3.5 w-3.5 text-blue-500" />
                    <span className="text-xs font-medium gradient-text-safe gradient-text-fallback">
                      {isThinking ? "DeepSeek 正在思考" : "DeepSeek 思考过程"}
                    </span>
                  </div>
                  {isThinking && (
                    <div className="flex items-center">
                      <span className="animate-pulse mr-1 gradient-text-safe gradient-text-fallback">•</span>
                      <span className="animate-pulse gradient-text-safe gradient-text-fallback" style={{animationDelay: "300ms"}}>•</span>
                      <span className="animate-pulse gradient-text-safe gradient-text-fallback" style={{animationDelay: "600ms"}}>•</span>
                    </div>
                  )}
                </div>
              </Button>
            </CollapsibleTrigger>
            <CollapsibleContent className="block">
              <div
                ref={streamingThinkingRef}
                className="p-3 text-sm text-muted-foreground bg-gradient-to-r from-blue-50/40 to-purple-50/40 dark:from-blue-900/20 dark:to-purple-900/20 border-t border-blue-200/60 dark:border-blue-800/60 max-h-[240px] overflow-y-auto scrollbar-thin"
              >
                <ReactMarkdown
                  remarkPlugins={[remarkGfm]}
                  rehypePlugins={[rehypeRaw]}
                  components={{
                    p: props => <p className="my-1.5" {...props} />,
                    li: props => <li className="my-1" {...props} />,
                    ul: props => <ul className="pl-5 list-disc my-2" {...props} />,
                    ol: props => <ol className="pl-5 list-decimal my-2" {...props} />
                  }}
                >
                  {currentThinkingContent}
                </ReactMarkdown>
              </div>
            </CollapsibleContent>
          </Collapsible>
        </div>
      );
    };

    return (
      <>
        {renderThinkingSection()}
        <ReactMarkdown
          remarkPlugins={[remarkGfm, remarkMath]}
          rehypePlugins={role === "assistant" ? [rehypeRaw, rehypeHighlight] : [rehypeHighlight]}
          components={{
            // 处理数学公式节点
            span(props) {
              const { className, children, ...rest } = props as {
                className?: string[]
                children?: React.ReactNode
                'data-math'?: string
                'data-inline'?: string
                [key: string]: unknown
              }
              const mathContent = rest['data-math'] as string
              const isInline = rest['data-inline'] === 'true'
              
              if (className?.includes('math-node') && mathContent) {
                return <MathBlock math={mathContent} inline={isInline} />
              }
              return <span className={className?.join(' ')} {...rest}>{children}</span>
            },
            code(props) {
              const {children, className, node, ...rest} = props;

              // 检查是否为内联代码（通过className判断）
              if (!className) {
                return <code className="px-1 py-0.5 rounded-sm bg-muted font-mono text-sm" {...rest}>{children}</code>;
              }

              const match = /language-(\w+)/.exec(className);

              // 改进代码内容提取逻辑
              // 将原始的children直接传递给CodeBlock组件，由其处理复杂的提取逻辑
              // 这样可以避免多次转换导致的信息丢失

              // 仅为了兼容其他特殊类型的代码块，我们还需要提取一个简单的字符串版本
              let codeContent = '';

              // 如果是字符串，直接使用
              if (typeof children === 'string') {
                codeContent = children;
              }
              // 如果是数组，尝试连接所有字符串元素
              else if (Array.isArray(children)) {
                codeContent = children
                  .map(child => (typeof child === 'string' ? child : ''))
                  .join('');
              }
              // 其他情况，使用基本的String转换
              else {
                try {
                  // 尝试使用JSON.stringify
                  const jsonStr = JSON.stringify(children);
                  if (jsonStr && jsonStr !== '{}') {
                    codeContent = jsonStr;
                  } else {
                    codeContent = String(children || '');
                  }
                } catch (e) {
                  codeContent = String(children || '');
                }
              }

              // 记录调试信息
              if (codeContent.includes('[object Object]')) {
                console.warn('ReactMarkdown code component received [object Object] in content');
                console.log('Original children type:', typeof children);
                if (typeof children === 'object') {
                  console.log('Children keys:', Object.keys(children as any));
                }
              }

              // 如果没有语言标识，返回普通代码块
              if (!match) {
                return <code className={cn("px-1 py-0.5 rounded-sm bg-muted font-mono text-sm", className)} {...rest}>{children}</code>;
              }

              const language = match[1];

              if (language === 'mermaid') {
                // 清理代码
                const cleanedCode = cleanMermaidCode(codeContent);

                // 为此图表生成唯一ID
                const contentHash = generateChartCacheKey(cleanedCode);

                return (
                  <MermaidChart
                    key={contentHash}
                    chart={codeContent}
                    onRenderComplete={() => {
                      if (pendingChartsRef.current > 0) {
                        pendingChartsRef.current -= 1;
                        console.log(`图表渲染完成，剩余${pendingChartsRef.current}个`);
                        setChartsRendering(prev => Math.max(0, prev - 1));
                      }
                    }}
                  />
                );
              }

              if (language === 'chart') {
                return <ChartBlock chartData={codeContent} />;
              }

              if (language === 'echarts') {
                return <EChartsBlock chartData={codeContent} />;
              }

              // 处理HTML代码块 - 只在助手消息中渲染为实际HTML表单
              if (language === 'html' && role === "assistant") {
                return (
                  <HTMLFormRenderer
                    key={`html-form-${typeof children === 'string' ? children.substring(0, 20) : 'non-string'}`}
                    htmlContent={codeContent}
                    onFormSubmit={(formData, formElement) => {
                      // 使用配置化的表单处理系统
                      console.log('HTML表单提交:', formData, formElement)

                      try {
                        // 匹配表单处理配置
                        const handlerConfig = matchFormHandler(formElement, formData)

                        if (handlerConfig) {
                          const { handler } = handlerConfig

                          switch (handler.type) {
                            case 'message':
                              if (handler.messageTemplate && onSendMessage) {
                                const message = formatFormData(formData, handler.messageTemplate)
                                onSendMessage(message)
                              }
                              break

                            case 'custom':
                              if (handler.customHandler) {
                                handler.customHandler(formData, formElement, onSendMessage)
                              }
                              break

                            case 'api':
                              // 可以在这里实现API调用逻辑
                              console.log('API调用:', handler.apiEndpoint)
                              break
                          }

                          // 显示成功消息
                          if (handler.successMessage) {
                            toast.success(handler.successMessage)
                          }
                        } else {
                          // 如果没有匹配的配置，使用默认处理
                          console.warn('未找到匹配的表单处理配置，使用默认处理')
                          handleDefaultForm(formData, formElement)
                        }
                      } catch (error) {
                        console.error('表单处理失败:', error)
                        toast.error('表单处理失败，请重试')
                      }
                    }}
                  />
                )
              }

              // 直接传递原始的children到CodeBlock组件
              // 这样可以避免多次转换导致的信息丢失
              // 添加key属性确保组件正确更新
              return (
                <CodeBlock
                  key={`code-${language}-${typeof children === 'string' ? children.substring(0, 20) : 'non-string'}`}
                  language={language}
                >
                  {children}
                </CodeBlock>
              );
            },
            a(props) {
              // 使用ExternalBrowserLink组件打开链接
              return (
                <ExternalBrowserLink
                  href={props.href || '#'}
                  className="text-primary underline break-all inline-flex items-center gap-1"
                  fallback={true}
                >
                  {props.children}
                </ExternalBrowserLink>
              )
            },
            // 改进引用块样式
            blockquote(props) {
              return <blockquote {...props} className="border-l-4 border-primary/50 bg-muted/30 pl-4 py-1 my-2 rounded-r-md" />
            },
            table({...props}) {
              return (
                <div className="my-4 rounded-lg border border-border shadow-sm bg-background" style={{
                  width: '100%',
                  maxWidth: '100%',
                  position: 'relative',
                  // 关键：使用 contain 只限制内联方向，不影响块方向
                  contain: 'layout inline-size',
                  // 创建新的层叠上下文，但不影响高度
                  isolation: 'isolate'
                }}>
                  <div
                    className="overflow-x-auto scrollbar-thin scrollbar-track-transparent scrollbar-thumb-gray-300 hover:scrollbar-thumb-gray-400"
                    style={{
                      // 确保滚动容器不会影响外部布局
                      maxWidth: '100%',
                      // 允许垂直方向自然扩展
                      height: 'auto'
                    }}
                  >
                    <table
                      className="border-collapse"
                      style={{
                        // 让表格自然扩展，但限制在合理范围内
                        width: 'max-content',
                        minWidth: '100%',
                        maxWidth: 'none'
                      }}
                      {...props}
                    />
                  </div>
                </div>
              )
            },
            thead({...props}) {
              return <thead className="bg-muted/30 border-b-2 border-border" {...props} />
            },
            th({...props}) {
              return <th
                className="border-r last:border-r-0 px-4 py-2 text-left font-semibold bg-muted/50 whitespace-nowrap"
                style={{
                  minWidth: '100px',  // 合理的最小宽度
                  maxWidth: 'none'    // 允许根据内容扩展
                }}
                {...props}
              />
            },
            td({...props}) {
              return <td
                className="border-r last:border-r-0 border-t px-4 py-2 whitespace-nowrap"
                style={{
                  minWidth: '100px',  // 合理的最小宽度
                  maxWidth: 'none'    // 允许根据内容扩展
                }}
                {...props}
              />
            },
            p(props) {
              // 检查子元素中是否有块级元素、图片或数学公式组件
              const hasBlockElement = React.Children.toArray(props.children).some(
                child => {
                  if (!React.isValidElement(child)) return false;
                  
                  const childType = child.type as any;
                  const typeName = childType?.displayName || childType?.name || (typeof childType === 'string' ? childType : '');
                  
                  // 检查是否为块级HTML元素
                  if (['div', 'table', 'ul', 'ol', 'blockquote', 'img'].includes(typeName)) {
                    return true;
                  }
                  
                  // 检查是否为数学公式组件（可能是块级显示）
                  if (typeName === 'MathBlock') {
                    // 如果是数学公式组件且不是内联模式，则认为是块级元素
                    const isInline = child.props?.inline;
                    return !isInline; // 非内联的数学公式是块级元素
                  }
                  
                  // 检查是否包含math-node类的span元素（可能包含块级数学公式）
                  if (typeName === 'span' && child.props?.className?.includes('math-node')) {
                    const isInline = child.props['data-inline'] === 'true';
                    return !isInline; // 非内联的数学公式是块级元素
                  }
                  
                  return false;
                }
              );

              // 如果有块级元素、图片或块级数学公式，使用div代替p标签
              if (hasBlockElement) {
                return <div className="my-2 last:mb-0 first:mt-0">{props.children}</div>;
              }

              // 否则正常渲染p标签
              return <p className="my-2 last:mb-0 first:mt-0" {...props} />
            },
            h1(props) {
              return <h1 className="text-2xl font-bold mb-4 pb-2 border-b mt-3" {...props} />
            },
            h2(props) {
              return <h2 className="text-xl font-bold mb-3 mt-4" {...props} />
            },
            h3(props) {
              return <h3 className="text-lg font-bold mb-2 mt-3" {...props} />
            },
            ul(props) {
              return <ul className="pl-6 my-3 list-disc" {...props} />
            },
            ol(props) {
              return <ol className="pl-6 my-3 list-decimal" {...props} />
            },
            li(props) {
              return <li className="mb-1.5" {...props} />
            },
            hr(props) {
              return <hr className="my-4 border-muted" {...props} />
            },
            img(props) {
              const [isImageDialogOpen, setIsImageDialogOpen] = useState(false)
              
              // 生成唯一的key基于图片src
              const imageKey = props.src ? `img-${props.src.substring(props.src.length - 20)}` : `img-${Math.random()}`

              return (
                <React.Fragment key={imageKey}>
                  <div className="my-3 flex justify-center">
                    <div
                      className="relative overflow-hidden rounded cursor-pointer group max-w-full"
                      onClick={() => setIsImageDialogOpen(true)}
                      title="点击查看大图"
                    >
                      <img
                        className="max-w-full max-h-[50vh] h-auto rounded-md object-contain"
                        alt={props.alt || ''}
                        {...props}
                      />
                      <div className="absolute inset-0 bg-black/0 group-hover:bg-black/10 flex items-center justify-center opacity-0 group-hover:opacity-100 transition-opacity">
                        <ZoomIn className="h-8 w-8 text-white drop-shadow-md" />
                      </div>
                    </div>
                  </div>

                  {/* 图片放大对话框 */}
                  <Dialog open={isImageDialogOpen} onOpenChange={setIsImageDialogOpen}>
                    <DialogContent className="max-w-[95vw] max-h-[95vh] p-0 overflow-hidden bg-transparent border-0" onClick={(e) => e.stopPropagation()}>
                      <DialogHeader className="sr-only">
                        <DialogTitle>查看图片</DialogTitle>
                      </DialogHeader>
                      <div className="relative w-full h-full flex items-center justify-center bg-black/10 backdrop-blur-sm p-2 sm:p-4">
                        <img
                          src={props.src}
                          alt={props.alt || ''}
                          className="object-contain max-h-[90vh] max-w-[90vw] rounded shadow-lg"
                        />
                        <DialogClose className="absolute top-2 right-2 bg-black/40 hover:bg-black/60 text-white rounded-full p-1">
                          <X className="h-5 w-5" />
                        </DialogClose>
                      </div>
                    </DialogContent>
                  </Dialog>
                </React.Fragment>
              )
            },
            // HTML 表单元素样式 - 只在 AI 回复中渲染，用户消息中显示为普通文本
            form(props) {
              if (role !== "assistant") {
                return <div className="font-mono text-sm bg-muted/50 p-2 rounded border">{props.children}</div>;
              }

              // 处理表单提交 - 使用配置化的表单处理系统
              const handleFormSubmit = (event: React.FormEvent<HTMLFormElement>) => {
                event.preventDefault()

                const form = event.currentTarget
                const formData = new FormData(form)

                console.log('ReactMarkdown表单提交:', formData, form)

                try {
                  // 匹配表单处理配置
                  const handlerConfig = matchFormHandler(form, formData)

                  if (handlerConfig) {
                    const { handler } = handlerConfig

                    switch (handler.type) {
                      case 'message':
                        if (handler.messageTemplate && onSendMessage) {
                          const message = formatFormData(formData, handler.messageTemplate)
                          onSendMessage(message)
                        }
                        break

                      case 'custom':
                        if (handler.customHandler) {
                          handler.customHandler(formData, form, onSendMessage)
                        }
                        break

                      case 'api':
                        // 可以在这里实现API调用逻辑
                        console.log('API调用:', handler.apiEndpoint)
                        break
                    }

                    // 显示成功消息
                    if (handler.successMessage) {
                      toast.success(handler.successMessage)
                    }
                  } else {
                    // 如果没有匹配的配置，使用默认处理
                    console.warn('未找到匹配的表单处理配置，使用默认处理')

                    // 收集表单数据
                    const data: Record<string, any> = {}
                    formData.forEach((value, key) => {
                      if (data[key]) {
                        // 处理多选情况
                        if (Array.isArray(data[key])) {
                          data[key].push(value)
                        } else {
                          data[key] = [data[key], value]
                        }
                      } else {
                        data[key] = value
                      }
                    })

                    // 显示提交成功消息
                    toast.success('表单提交成功！', {
                      description: `提交了 ${Object.keys(data).length} 个字段的数据`
                    })

                    // 发送表单数据作为新消息
                    if (onSendMessage) {
                      const dataEntries = Array.from(formData.entries())
                      const formDataText = dataEntries
                        .map(([key, value]) => `${key}: ${value}`)
                        .join('\n')

                      onSendMessage(`表单提交数据:\n${formDataText}`)
                    }
                  }
                } catch (error) {
                  console.error('表单处理失败:', error)
                  toast.error('表单处理失败，请重试')
                }
              }

              return (
                <form
                  {...props}
                  onSubmit={handleFormSubmit}
                  // className="bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg p-6 my-4 shadow-sm relative"
                  className="p-2 shadow-sm relative"
                />
              )
            },
            input(props) {
              if (role !== "assistant") {
                return <code className="font-mono text-sm bg-muted/50 px-1 rounded">&lt;input /&gt;</code>;
              }

              const { type = 'text', value, onChange, ...restProps } = props;

              if (type === 'submit' || type === 'button') {
                return (
                  // <div className="flex justify-end mt-6">
                  <div className="flex justify-end mt-1">
                    <input
                      {...restProps}
                      type={type}
                      value={value}
                      className="bg-primary hover:bg-primary/90 text-primary-foreground font-medium py-2.5 px-6 rounded-lg transition-all duration-200 cursor-pointer border-0 shadow-sm hover:shadow-md"
                    />
                  </div>
                );
              }

              if (type === 'checkbox' || type === 'radio') {
                return (
                  <input
                    {...restProps}
                    type={type}
                    defaultChecked={value === 'true' || value === 'checked' || value === 'on' || Boolean(value)}
                    className="w-4 h-4 text-primary bg-background border-2 border-border rounded focus:ring-2 focus:ring-primary/20 transition-colors"
                  />
                );
              }

              // 对于datetime-local和datetime类型，使用自定义组件
              if (type === 'datetime-local' || type === 'datetime') {
                return (
                  <div className="mb-4">
                    <DateTimePicker
                      value={value as string}
                      onChange={() => {}} // 在聊天消息中只用于显示，不需要onChange
                      placeholder="选择日期和时间"
                      name={restProps.name}
                      id={restProps.id}
                      required={restProps.required}
                    />
                  </div>
                );
              }

              // 对于其他类型的input，使用defaultValue而不是value来避免只读问题
              const inputClassName = 'w-full px-4 py-3 bg-white dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all duration-200 text-gray-900 dark:text-gray-100 placeholder:text-gray-500 dark:placeholder:text-gray-400';

              return (
                <div className="mb-4">
                  <input
                    {...restProps}
                    type={type}
                    defaultValue={value}
                    className={inputClassName}
                  />
                </div>
              );
            },
            textarea(props) {
              if (role !== "assistant") {
                return <code className="font-mono text-sm bg-muted/50 px-1 rounded">&lt;textarea&gt;&lt;/textarea&gt;</code>;
              }

              const { value, onChange, ...restProps } = props;
              const textareaClassName = 'w-full px-4 py-3 bg-white dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all duration-200 text-gray-900 dark:text-gray-100 placeholder:text-gray-500 dark:placeholder:text-gray-400 resize-vertical min-h-[100px]';

              return (
                <div className="mb-4">
                  <textarea
                    {...restProps}
                    defaultValue={value}
                    className={textareaClassName}
                  />
                </div>
              );
            },
            select(props) {
              if (role !== "assistant") {
                return <code className="font-mono text-sm bg-muted/50 px-1 rounded">&lt;select&gt;&lt;/select&gt;</code>;
              }

              const { value, onChange, ...restProps } = props;
              const selectClassName = 'w-full px-4 py-3 bg-white dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all duration-200 text-gray-900 dark:text-gray-100';

              return (
                <div className="mb-4">
                  <select
                    {...restProps}
                    defaultValue={value}
                    className={selectClassName}
                  />
                </div>
              );
            },
            button(props) {
              if (role !== "assistant") {
                return <code className="font-mono text-sm bg-muted/50 px-1 rounded">&lt;button&gt;{props.children}&lt;/button&gt;</code>;
              }

              const { type = 'button', ...restProps } = props;

              // 如果是提交按钮，放在右下角
              if (type === 'submit') {
                return (
                  // <div className="flex justify-end mt-6">
                  <div className="flex justify-end mt-1">
                    <button
                      {...restProps}
                      type={type}
                      className="bg-primary hover:bg-primary/90 text-primary-foreground font-medium py-2.5 px-6 rounded-lg transition-all duration-200 border-0 shadow-sm hover:shadow-md cursor-pointer"
                    />
                  </div>
                );
              }

              return (
                <button
                  {...restProps}
                  type={type}
                  className="bg-primary hover:bg-primary/90 text-primary-foreground font-medium py-2.5 px-6 rounded-lg transition-all duration-200 border-0 shadow-sm hover:shadow-md cursor-pointer"
                />
              );
            },
            label(props) {
              if (role !== "assistant") {
                return <code className="font-mono text-sm bg-muted/50 px-1 rounded">&lt;label&gt;{props.children}&lt;/label&gt;</code>;
              }

              const { children, className, ...restProps } = props;

              // 使用useEffect来检查关联的input是否有required属性
              const labelRef = useRef<HTMLLabelElement>(null);
              const [isRequired, setIsRequired] = useState(false);

              useEffect(() => {
                // 检查label自身是否有required属性（虽然这不是标准HTML，但有些情况下会出现）
                const labelHasRequired = (props as any).required || (restProps as any).required;
                if (labelHasRequired) {
                  setIsRequired(true);
                  return;
                }

                if (labelRef.current && props.htmlFor) {
                  // 首先尝试精确匹配ID
                  let formElement = document.getElementById(props.htmlFor);

                  // 如果找不到精确匹配，尝试查找附近的表单元素
                  if (!formElement && labelRef.current.parentElement) {
                    // 查找同一个容器内的表单元素
                    const container = labelRef.current.parentElement;
                    const formElements = container.querySelectorAll('input, select, textarea');

                    // 如果只有一个表单元素，假设它就是关联的元素
                    if (formElements.length === 1) {
                      formElement = formElements[0] as HTMLElement;
                    } else {
                      // 尝试根据type或name属性匹配
                      for (const element of formElements) {
                        const inputElement = element as HTMLInputElement;
                        if (inputElement.type === props.htmlFor ||
                            inputElement.name === props.htmlFor ||
                            inputElement.id?.includes(props.htmlFor) ||
                            props.htmlFor?.includes(inputElement.id || '')) {
                          formElement = inputElement;
                          break;
                        }
                      }
                    }
                  }

                  if (formElement && formElement.hasAttribute('required')) {
                    setIsRequired(true);
                  }
                }
              }, [props.htmlFor]);

              return (
                <label
                  {...restProps}
                  ref={labelRef}
                  className={`block text-base font-medium text-gray-700 dark:text-gray-300 mb-1.5 ${className || ''}`}
                >
                  {children}
                  {isRequired && (
                    <span className="text-red-500 ml-1">*</span>
                  )}
                </label>
              );
            },
            fieldset(props) {
              if (role !== "assistant") {
                return <div className="font-mono text-sm bg-muted/50 p-2 rounded border">{props.children}</div>;
              }
              return (
                <fieldset
                  {...props}
                  className="border border-border rounded-lg p-4 my-4 bg-muted/30"
                />
              );
            },
            legend(props) {
              if (role !== "assistant") {
                return <code className="font-mono text-sm bg-muted/50 px-1 rounded">&lt;legend&gt;{props.children}&lt;/legend&gt;</code>;
              }
              return (
                <legend
                  {...props}
                  className="text-base font-semibold text-foreground px-3 bg-background"
                />
              );
            }
          }}
        >
          {/* 处理转义的换行符并清理思考过程标记 */}
          {cleanDisplayContent(displayedContent).replace(/\\n/g, '\n')}
        </ReactMarkdown>
      </>
    );
  };

  return (
    <div className="relative flex-1 overflow-auto [&::-webkit-scrollbar]:hidden [-ms-overflow-style:none] [scrollbar-width:none]" ref={scrollAreaRef} data-tour="chat-messages">


      <ScrollArea className="h-full [&::-webkit-scrollbar]:hidden [-ms-overflow-style:none] [scrollbar-width:none]" style={{ overflow: 'visible' }}>
        <div className="pt-4 pb-2 px-4 space-y-12 flex flex-col md:items-center items-start">
          {messages.map((message, index) => {
            // 处理由MessageType提供的thinking字段或从内容中提取的思考过程
            let thinking = message.thinking
            let mainContent = message.content
            let isStreamFinished = !message.isStreaming

            // 只对助手消息提取思考过程，用户消息中的思考标签保持原样
            if (!thinking && !message.isThinking && isStreamFinished && message.role === "assistant") {
              const extracted = extractThinkingProcess(message.content || "")
              thinking = extracted.thinkingContent
              mainContent = extracted.mainContent
              // 保存使用的标记格式
              message.marker = extracted.marker
            }

            // 特别检查是否为 DeepSeek-R1 格式
            if (!message.marker && thinking && message.role === "assistant" && message.content?.includes("<think>")) {
              message.marker = "<think>"
            }

            // 查找助手消息对应的用户问题
            let userQuestion = "";
            if (message.role === "assistant" && index > 0) {
              // 查找前一条消息，如果是用户消息就获取内容
              const prevMessage = messages[index - 1];
              if (prevMessage && prevMessage.role === "user") {
                userQuestion = prevMessage.content;
              }
            }

            return (
              <div
                key={message.id}
                className={cn(
                  "w-full",
                  message.role === "user" ? "flex justify-end" : "flex justify-start"
                )}
                style={{
                  // 确保用户消息容器不被表格影响，但不高于Dialog层级
                  position: 'relative',
                  zIndex: message.role === "user" ? 10 : 'auto'
                }}
              >
                <div
                  className={cn(
                    "flex gap-3",
                    message.role === "user" ? "flex-row-reverse" : "flex-row"
                  )}
                  style={{
                    width: message.role === "user"
                    ? (message.content && /```(mermaid|chart|echarts)/i.test(message.content)
                        ? messageWidth
                        : "auto")
                    : mermaidMessageWidth, // 所有AI消息都使用更大的宽度
                    maxWidth: message.role === "assistant"
                        ? mermaidMessageWidth // 所有AI消息都使用更大的最大宽度
                        : messageWidth,
                    flexShrink: 0
                  }}
                >
                  <ChatAvatar
                    type={message.role === "user" ? "user" : "bot"}
                    className="mt-1"
                  />
                  <div className={cn(
                      "p-3 rounded-lg break-words shadow-sm",
                      message.role === "user"
                        ? (message.content && /```(mermaid|chart|echarts)/i.test(message.content)) || (message.attachments && message.attachments.length > 0)
                          // 当用户消息包含图表或附件时，使用浅蓝色背景，不使用prose类避免样式冲突
                          ? "bg-blue-50 dark:bg-blue-950/30 w-full"
                          // 普通用户消息使用浅蓝色背景
                          : "bg-blue-50 dark:bg-blue-950/30 prose prose-sm prose-zinc dark:prose-invert min-h-[48px] inline-flex flex-col justify-center"
                        : "bg-white dark:bg-gray-800 prose prose-sm prose-zinc dark:prose-invert w-full border border-gray-200 dark:border-gray-700"
                    )}
                    style={{
                      wordBreak: "break-word",
                      overflowWrap: "break-word",
                      whiteSpace: "normal"
                    }}
                    {...(message.role === "assistant" && index === messages.length - 1 && {
                      "data-tour": "last-ai-message"
                    })}>
                    {message.attachments && message.attachments.length > 0 && (
                      <div className={cn(
                        "space-y-2",
                        message.role === "user" && message.content
                          ? "mb-2 pb-2 border-b border-muted-foreground/20" // 用户消息有文本内容时添加分割线
                          : "mb-2" // 没有文本内容时只添加底部边距
                      )}>
                        {message.attachments.map((attachment) => (
                          <AttachmentPreview key={attachment.id} attachment={attachment} role={message.role} />
                        ))}
                      </div>
                    )}

                    {message.isThinking ? (
                      <ThinkingIndicator />
                    ) : (
                      <>
                        {/* 只有在非流式输出完成后，才显示外部的思考过程组件 */}
                        {thinking && message.role === "assistant" && !message.isStreaming && (
                          <ThinkingSection thinking={thinking} marker={message.marker} />
                        )}

                        <div className={cn(
                          message.role === "user" ? "w-full" : "w-full",
                          message.role === "assistant" && "overflow-y-auto pr-1 custom-scrollbar [&::-webkit-scrollbar]:hidden [-ms-overflow-style:none] [scrollbar-width:none]"
                        )}>
                          {/* 用户消息始终显示，助手消息需要有内容才显示 */}
                          {(message.role === "user" || mainContent) && (
                            message.isStreaming ? (
                              // 流式输出时，由TypewriterText组件完全处理思考过程和主内容
                              <ErrorBoundary>
                                <TypewriterText
                                  content={message.content}
                                  isStreaming={true}
                                  role={message.role}
                                  onSendMessage={onSendMessage}
                                />
                              </ErrorBoundary>
                            ) : (
                              // 非流式消息处理，这里只显示主内容，思考过程由外部组件显示
                              <ErrorBoundary>
                                <TypewriterText
                                  content={message.role === "user" ? (message.content || "") : mainContent}
                                  isStreaming={false}
                                  showThinking={false}
                                  role={message.role}
                                  onSendMessage={onSendMessage}
                                />
                              </ErrorBoundary>
                            )
                          )}
                        </div>

                        {/* 添加消息操作按钮和耗时显示 */}
                        {!message.isThinking && (message.role === "user" || mainContent) && (
                          <div className="flex justify-between items-center mt-2">
                            <div data-tour="message-actions">
                              <MessageActions
                                content={message.role === "user" ? (message.content || "") : mainContent}
                                messageId={message.id}
                                role={message.role}
                                onRegenerate={message.role === 'assistant' ? onRegenerateMessage : undefined}
                                userQuestion={userQuestion}
                                userAttachments={message.role === 'assistant' && index > 0 ?
                                  (messages[index - 1]?.role === 'user' ? messages[index - 1]?.attachments : undefined) :
                                  undefined}
                              />
                            </div>

                            {/* 显示处理时间或流式loading */}
                            {message.role === "assistant" && (
                              <div className="text-xs text-muted-foreground flex items-center gap-1">
                                {message.isStreaming ? (
                                  <>
                                    <ModernLoader size={12} className="opacity-70" />
                                    <span>正在回复...</span>
                                  </>
                                ) : chartsRendering > 0 && (message.content?.includes('```mermaid') || message.content?.includes('```chart') || message.content?.includes('```echarts')) ? (
                                  <>
                                    <ModernLoader size={12} className="opacity-70" />
                                    <span>图表渲染中...</span>
                                  </>
                                ) : message.processTime ? (
                                  <span>耗时 {(message.processTime / 1000).toFixed(1)} 秒</span>
                                ) : null}
                              </div>
                            )}
                          </div>
                        )}

                        {/* 在第一条AI消息后显示反馈引导 */}
                        {showFeedbackGuide && message.role === "assistant" && index === 1 && !message.isStreaming && !message.isThinking && (
                          <FeedbackGuide onClose={handleCloseFeedbackGuide} />
                        )}
                      </>
                    )}
                  </div>
                </div>
              </div>
            )
          })}
          <div ref={messagesEndRef} className="h-2" />
        </div>
      </ScrollArea>

      {/* 滚动回底部按钮 */}
      {!isScrolledToBottom && messages.length > 2 && (
        <Button
          className="absolute bottom-0.5 right-3 rounded-full opacity-90 shadow-md z-10"
          size="sm"
          onClick={() => {
            userScrolledUp.current = false;
            setIsAutoScrollEnabled(true);
            scrollToBottom();
          }}
        >
          <ChevronDown className="mr-1 h-3.5 w-3.5" />
          滚动到底部
        </Button>
      )}

    </div>
  )
}
